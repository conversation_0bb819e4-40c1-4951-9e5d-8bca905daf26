<?php

namespace Database\Seeders;

use <PERSON><PERSON>ble\Base\Supports\BaseSeeder;
use Botble\Ecommerce\Models\ProductTag;
use <PERSON><PERSON>ble\Slug\Facades\SlugHelper;
use <PERSON><PERSON>ble\Slug\Models\Slug;
use Illuminate\Support\Str;

class ProductTagSeeder extends BaseSeeder
{
    public function run(): void
    {
        $tags = [
            [
                'name' => 'Electronic',
            ],
            [
                'name' => 'Mobile',
            ],
            [
                'name' => 'Iphone',
            ],
            [
                'name' => 'Printer',
            ],
            [
                'name' => 'Office',
            ],
            [
                'name' => 'IT',
            ],
        ];

        ProductTag::query()->truncate();

        foreach ($tags as $item) {
            $tag = ProductTag::query()->create($item);

            Slug::query()->create([
                'reference_type' => ProductTag::class,
                'reference_id' => $tag->id,
                'key' => Str::slug($tag->name),
                'prefix' => SlugHelper::getPrefix(ProductTag::class),
            ]);
        }
    }
}
