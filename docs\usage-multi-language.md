# Multi-language

## Setting Up Languages

Navigate to admin panel and click to `Settings` -> `Languages`.

![Multi Language](../cms/images/multi-language-1.png)

## Changing the Default Language

### Adding a New Language

To change the default language, you first need to add the desired language. In this example, we will add **Tiếng Việt
** (Vietnamese) language.

![Adding a New Language](../cms/images/multi-language-2.png)

### Setting the Default Language

Once the language is added, click on the **Is default?** button in the **Tiếng Việt** row to make it the default
language.

![Setting the Default Language](../cms/images/multi-language-3.png)

## Disabling Multi-language

Navigate to `Plugins` -> `Installed Plugins` and type **language** in the search bar. Find the **Language**
and **Language Advanced** plugins and disable them to disable multi-language functionality.

![](../cms/images/multi-language-4.png)
